/*
 * 标签管理页面 - 简化版本
 * 提供标签的创建、编辑、删除和管理功能
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Controls.Material 2.15

Rectangle {
    id: root
    color: "#f5f5f5"

    // 对外暴露的属性
    property var tagList: []
    property bool isLoading: false

    // 对外暴露的信号
    signal createTag(var tagData)
    signal updateTag(int tagId, var tagData)
    signal deleteTag(int tagId)
    signal refreshRequested()

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Label {
            text: "🏷️ 标签管理"
            font.bold: true
            font.pixelSize: 24
            color: "#333"
            Layout.alignment: Qt.AlignHCenter
        }

        // 操作按钮栏
        Rectangle {
            Layout.fillWidth: true
            height: 60
            color: "white"
            radius: 8
            border.color: "#e0e0e0"

            RowLayout {
                anchors.fill: parent
                anchors.margins: 15
                spacing: 10

                Label {
                    text: "操作:"
                    font.pixelSize: 14
                    color: "#666"
                }

                Button {
                    text: "➕ 创建标签"
                    Material.background: Material.Blue
                    onClicked: createTagDialog.open()
                }

                Button {
                    text: "🔄 刷新"
                    onClicked: root.refreshRequested()
                }

                Item { Layout.fillWidth: true }

                Label {
                    text: "共 " + root.tagList.length + " 个标签"
                    font.pixelSize: 14
                    color: "#666"
                }
            }
        }

        // 标签列表区域
        Rectangle {
            Layout.fillWidth: true
            Layout.fillHeight: true
            color: "white"
            radius: 8
            border.color: "#e0e0e0"

            ColumnLayout {
                anchors.fill: parent
                anchors.margins: 20
                spacing: 15

                // 列表标题
                Label {
                    text: "标签列表"
                    font.bold: true
                    font.pixelSize: 16
                    color: "#333"
                }

                // 标签列表
                ListView {
                    id: tagListView
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    visible: !root.isLoading && root.tagList.length > 0

                    model: root.tagList
                    spacing: 8

                    delegate: Rectangle {
                        width: tagListView.width
                        height: 80
                        color: "white"
                        radius: 4
                        border.color: "#e0e0e0"
                        border.width: 1

                        RowLayout {
                            anchors.fill: parent
                            anchors.margins: 15
                            spacing: 15

                            // 标签图标和颜色
                            Rectangle {
                                width: 40
                                height: 40
                                color: modelData.color || "#2196F3"
                                radius: 20

                                Label {
                                    anchors.centerIn: parent
                                    text: modelData.icon || "🏷️"
                                    font.pixelSize: 16
                                }
                            }

                            // 标签信息
                            ColumnLayout {
                                Layout.fillWidth: true
                                spacing: 5

                                Label {
                                    text: modelData.name || ""
                                    font.pixelSize: 14
                                    font.weight: Font.DemiBold
                                    color: "#333"
                                }

                                Label {
                                    text: modelData.description || "无描述"
                                    font.pixelSize: 12
                                    color: "#666"
                                }
                            }

                            // 使用统计
                            Label {
                                text: (modelData.usage_count || 0) + " 次使用"
                                font.pixelSize: 12
                                color: "#999"
                            }

                            // 操作按钮
                            RowLayout {
                                spacing: 5

                                Button {
                                    text: "✏️"
                                    font.pixelSize: 12
                                    implicitWidth: 30
                                    implicitHeight: 30
                                    ToolTip.text: "编辑"
                                    onClicked: {
                                        editTagDialog.tagData = modelData
                                        editTagDialog.open()
                                    }
                                }

                                Button {
                                    text: "🗑️"
                                    font.pixelSize: 12
                                    implicitWidth: 30
                                    implicitHeight: 30
                                    Material.background: Material.Red
                                    ToolTip.text: "删除"
                                    onClicked: {
                                        deleteConfirmDialog.tagId = modelData.id
                                        deleteConfirmDialog.tagName = modelData.name
                                        deleteConfirmDialog.open()
                                    }
                                }
                            }
                        }
                    }
                }

                // 空状态显示
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    visible: !root.isLoading && root.tagList.length === 0
                    spacing: 20

                    Item { Layout.fillHeight: true }

                    Label {
                        text: "📝"
                        font.pixelSize: 48
                        color: "#ccc"
                        Layout.alignment: Qt.AlignHCenter
                    }

                    Label {
                        text: "暂无标签"
                        font.pixelSize: 16
                        color: "#666"
                        Layout.alignment: Qt.AlignHCenter
                    }

                    Label {
                        text: "创建第一个标签来开始管理您的邮箱"
                        font.pixelSize: 14
                        color: "#999"
                        Layout.alignment: Qt.AlignHCenter
                    }

                    Item { Layout.fillHeight: true }
                }

                // 加载指示器
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    visible: root.isLoading
                    spacing: 20

                    Item { Layout.fillHeight: true }

                    BusyIndicator {
                        Layout.alignment: Qt.AlignHCenter
                        running: root.isLoading
                    }

                    Label {
                        text: "正在加载标签列表..."
                        font.pixelSize: 14
                        color: "#666"
                        Layout.alignment: Qt.AlignHCenter
                    }

                    Item { Layout.fillHeight: true }
                }
            }
        }
    }

    // 创建标签对话框
    Dialog {
        id: createTagDialog
        title: "创建标签"
        modal: true
        anchors.centerIn: parent
        width: 400

        ColumnLayout {
            spacing: 15
            width: parent.width

            TextField {
                id: createNameField
                Layout.fillWidth: true
                placeholderText: "标签名称..."
            }

            TextField {
                id: createDescField
                Layout.fillWidth: true
                placeholderText: "标签描述..."
            }

            TextField {
                id: createIconField
                Layout.fillWidth: true
                placeholderText: "图标 (如: 🏷️)..."
                text: "🏷️"
            }

            TextField {
                id: createColorField
                Layout.fillWidth: true
                placeholderText: "颜色 (如: #2196F3)..."
                text: "#2196F3"
            }

            RowLayout {
                Layout.alignment: Qt.AlignRight
                spacing: 10

                Button {
                    text: "取消"
                    onClicked: {
                        createNameField.text = ""
                        createDescField.text = ""
                        createIconField.text = "🏷️"
                        createColorField.text = "#2196F3"
                        createTagDialog.close()
                    }
                }

                Button {
                    text: "创建"
                    Material.background: Material.Blue
                    enabled: createNameField.text.trim().length > 0
                    onClicked: {
                        var tagData = {
                            name: createNameField.text.trim(),
                            description: createDescField.text.trim(),
                            icon: createIconField.text.trim() || "🏷️",
                            color: createColorField.text.trim() || "#2196F3"
                        }
                        root.createTag(tagData)
                        createNameField.text = ""
                        createDescField.text = ""
                        createIconField.text = "🏷️"
                        createColorField.text = "#2196F3"
                        createTagDialog.close()
                    }
                }
            }
        }
    }

    // 编辑标签对话框
    Dialog {
        id: editTagDialog
        title: "编辑标签"
        modal: true
        anchors.centerIn: parent
        width: 400

        property var tagData: ({})

        ColumnLayout {
            spacing: 15
            width: parent.width

            TextField {
                id: editNameField
                Layout.fillWidth: true
                placeholderText: "标签名称..."
                text: editTagDialog.tagData.name || ""
            }

            TextField {
                id: editDescField
                Layout.fillWidth: true
                placeholderText: "标签描述..."
                text: editTagDialog.tagData.description || ""
            }

            TextField {
                id: editIconField
                Layout.fillWidth: true
                placeholderText: "图标 (如: 🏷️)..."
                text: editTagDialog.tagData.icon || "🏷️"
            }

            TextField {
                id: editColorField
                Layout.fillWidth: true
                placeholderText: "颜色 (如: #2196F3)..."
                text: editTagDialog.tagData.color || "#2196F3"
            }

            RowLayout {
                Layout.alignment: Qt.AlignRight
                spacing: 10

                Button {
                    text: "取消"
                    onClicked: editTagDialog.close()
                }

                Button {
                    text: "保存"
                    Material.background: Material.Blue
                    enabled: editNameField.text.trim().length > 0
                    onClicked: {
                        var updatedData = {
                            id: editTagDialog.tagData.id,
                            name: editNameField.text.trim(),
                            description: editDescField.text.trim(),
                            icon: editIconField.text.trim() || "🏷️",
                            color: editColorField.text.trim() || "#2196F3"
                        }
                        root.updateTag(editTagDialog.tagData.id, updatedData)
                        editTagDialog.close()
                    }
                }
            }
        }
    }

    // 删除确认对话框
    Dialog {
        id: deleteConfirmDialog
        title: "确认删除"
        modal: true
        anchors.centerIn: parent

        property int tagId: 0
        property string tagName: ""

        ColumnLayout {
            spacing: 20

            Label {
                text: "确定要删除标签 \"" + deleteConfirmDialog.tagName + "\" 吗？\n删除后，使用此标签的邮箱将失去此标签。"
                wrapMode: Text.WordWrap
                Layout.preferredWidth: 300
            }

            RowLayout {
                Layout.alignment: Qt.AlignRight
                spacing: 10

                Button {
                    text: "取消"
                    onClicked: deleteConfirmDialog.close()
                }

                Button {
                    text: "删除"
                    Material.background: Material.Red
                    onClicked: {
                        root.deleteTag(deleteConfirmDialog.tagId)
                        deleteConfirmDialog.close()
                    }
                }
            }
        }
    }
}
